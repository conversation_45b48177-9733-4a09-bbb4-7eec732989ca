[project]
name = "zescond"
version = "0.1.0"
description = "Zescond is the project of ZEro-shot Quantization Object Detection"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "pydantic-settings>=2.7.0",
    "pydantic>=2.10.4",
    "torch>=2.5.1",
    "torchvision>=0.20.1",
    "ultralytics==8.3.53",
    "tensorboard>=2.18.0",
    "types-pyyaml>=6.0.12.20241230",
    "pyyaml>=6.0.2",
    "pycocotools>=2.0.8",
]

[[tool.uv.index]]
name = "Tencent"
url = "https://mirrors.cloud.tencent.com/pypi/simple"

[[tool.uv.index]]
name = "TUNA"
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"

[tool.mypy]
plugins = ['pydantic.mypy']
ignore_missing_imports = true

[dependency-groups]
dev = [
    "ipykernel>=6.29.5",
]
