[project]
name = "zescond-vit"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "mmcv-full==1.4.2",
    "mmdet==2.22.0",
    "pydantic-settings>=2.7.1",
    "pydantic>=2.10.6",
    "requests>=2.28.1",
    "scipy>=1.13.1",
    "setuptools==69.5.1",
    "timm==0.4.12",
    "torch==1.9.0+cu111",
    "torchaudio==0.9.0",
    "torchvision==0.10.0+cu111",
    "tqdm>=4.67.1",
]

[[tool.uv.index]]
name = "pytorch-cu111"
url = "https://download.pytorch.org/whl/cu111"

[[tool.uv.index]]
name = "Tencent"
url = "https://mirrors.cloud.tencent.com/pypi/simple"

[[tool.uv.index]]
name = "TUNA"
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"

[tool.mypy]
plugins = ['pydantic.mypy']
ignore_missing_imports = true

[dependency-groups]
dev = [
    "ipykernel>=6.29.5",
]
