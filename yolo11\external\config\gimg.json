{"device": "cuda", "dataset_manifest": "coco.yaml", "batch_size": 16, "fraction": 0.1, "generated_weights_path": "./runs/exp/weights", "calibration_size": 10000, "model": "yolo11s.pt", "model_quantize_mode": {"kind": "quantize_sym", "weight_bits": 8, "activation_bits": 8}, "kd_method": null, "hyps": {"optimizer_name": "<PERSON>", "lr0": 1e-05, "lrf": 0.01, "momentum": 0.937, "weight_decay": 0.0005, "box": 7.5, "cls": 0.5, "dfl": 1.5}, "end_epochs": 100, "patience": 50}