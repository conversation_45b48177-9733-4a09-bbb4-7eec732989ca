[project]
name = "resnet"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = "==3.9.23"
dependencies = [
    "lxml>=6.0.0",
    "matplotlib>=3.9.4",
    "numpy>=2.0.2",
    "pillow>=11.3.0",
    "pycocotools>=2.0.10",
    "torch==1.10.0",
    "torchvision==0.11.1",
    "tqdm>=4.67.1",
]

[[tool.uv.index]]
name = "TUNA"
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"

[[tool.uv.index]]
name = "Tencent"
url = "https://mirrors.cloud.tencent.com/pypi/simple"
